import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:wicker/services/config_service.dart';

class PaymentWebViewScreen extends StatefulWidget {
  final String authorizationUrl;

  const PaymentWebViewScreen({super.key, required this.authorizationUrl});

  @override
  State<PaymentWebViewScreen> createState() => _PaymentWebViewScreenState();
}

class _PaymentWebViewScreenState extends State<PaymentWebViewScreen> {
  // The controller is now nullable and initialized asynchronously.
  WebViewController? _controller;
  bool _isPageLoading = true;

  @override
  void initState() {
    super.initState();
    // We now initialize everything in an async method to prevent race conditions.
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    // 1. First, get the callback URL from our config service.
    final baseUrl = await ConfigService.instance.getBaseUrl();
    final callbackUrl = '$baseUrl/api/transactions/webhook';

    // 2. Now that we have the URL, create and configure the controller.
    final controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isPageLoading = false;
              });
            }
          },
          // 3. The delegate now reliably checks against the fully loaded callbackUrl.
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(callbackUrl)) {
              // Success! Pop the screen and return 'true'.
              Navigator.of(context).pop(true);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.authorizationUrl));

    // 4. Once the controller is configured, update the state.
    if (mounted) {
      setState(() {
        _controller = controller;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Your Payment'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Stack(
        children: [
          // Only build the WebViewWidget if the controller has been initialized.
          if (_controller != null) WebViewWidget(controller: _controller!),

          // Show a loading indicator until the controller is ready and the page loads.
          if (_isPageLoading || _controller == null)
            const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }
}
